/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IStatusBar
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/8/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.content.Context

interface ISeedlingStatusBar {

    fun show(ctx: Context, forceShow: Boolean = false, from: String)

    fun dismiss(ctx: Context, forceDismiss: Boolean = false, from: String)

    fun register()

    fun unRegister()

    fun release() {}
}