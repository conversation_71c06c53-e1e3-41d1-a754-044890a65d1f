/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertTaskApi.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : W9067780
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.api

import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager
import com.soundrecorder.modulerouter.convertService.ConvertTaskAction

object ConvertTaskApi : ConvertTaskAction {

    override fun startConvertTask(mediaId: Long) {
        ConvertTaskThreadManager.startOrResumeConvert(mediaId, ConvertSupportManager.getConvertSupportType(true), false)
    }
}
