/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/06
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.visible
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionPrivacyCallback
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.FUNC_TYPE_SUMMARY_MIND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface
import com.soundrecorder.modulerouter.summary.BUNDLE_MEDIA_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_FILE_PATH
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_MODIFY_TIME
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_TITLE
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_TYPE
import com.soundrecorder.modulerouter.summary.IAISummaryCallback
import com.soundrecorder.modulerouter.summary.IAISummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.databinding.LayoutSummaryFragmentBinding
import com.soundrecorder.summary.model.AISummaryViewModel
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryStop
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.request.database.SummaryCacheDao.Companion.MAX_COUNT
import com.soundrecorder.summary.ui.content.SummaryContentView
import com.soundrecorder.summary.ui.content.callback.ISummaryFunctionCallback
import com.soundrecorder.summary.util.AIUnitApi

class SummaryFragment : Fragment(), IAISummaryInterface {
    companion object {
        private const val TAG = "SummaryFragment"
    }

    private var binding: LayoutSummaryFragmentBinding? = null
    private var viewModel: AISummaryViewModel? = null
    private var summaryContentView: SummaryContentView? = null
    private var summaryStopLayout: LinearLayout? = null

    private var mediaId: Long = 0L
    private var recordType: Int = 0
    private var recordCreateTime: Long = 0L
    private var recordTitle: String = ""
    private var recordFilePath: String = ""
    private var isSelected = false
    private var warningDialog: AlertDialog? = null
    private var currentChooseTheme: SummaryTheme? = null
    private var summaryCallback: IAISummaryCallback? = null
    private var insetsBottom = 0
    private var paddingBottom = 0
    private val privacyAction by lazy {
        Injector.injectFactory<PrivacyPolicyInterface>()
    }

    private val callback: ISummaryFunctionCallback by lazy {
        object : ISummaryFunctionCallback {
            override fun onClickAgent(agents: List<SummaryAgentEvent>) {
                DebugUtil.e(TAG, "onClickAgent $agents")
                viewModel?.updateSummaryAgent(agents)
            }

            override fun onClickRefresh() {
                viewModel?.regenerateSummary(requireContext(), true)
            }

            override fun onClickRetry() {
                viewModel?.regenerateSummary(requireContext())
            }

            override fun onClickPrevious() {
                viewModel?.switchToPreviousSummary()
            }

            override fun onClickScene(theme: SummaryTheme?) {
                theme ?: run {
                    DebugUtil.e(TAG, "onClickScene theme is null")
                    return
                }
                currentChooseTheme = theme
                summaryContentView?.onStartLoading()
                viewModel?.regenerateSummary(requireContext(), true, theme = theme)
            }

            override fun onClickNext() {
                viewModel?.switchToNextSummary()
            }

            override fun onFinishAnimator() {
                DebugUtil.d(TAG, "onFinishAnimator")
                super.onFinishAnimator()
                //效果是文字上屏动画结束之后，才把暂停按钮消失，所以不能是finish的状态直接就处理，需要等view回调回来处理
                summaryCallback?.onSummaryEnd()
                summaryStopGone()
            }
        }
    }

    override fun getFragment(): Fragment {
        return this
    }

    override fun summaryFragmentSelected() {
        DebugUtil.d(TAG, "summaryFragmentSelected")
        isSelected = true
    }

    override fun summaryFragmentUnSelected() {
        DebugUtil.d(TAG, "summaryFragmentUnSelected")
        isSelected = false
    }

    override fun summaryFragmentScrollEnd(select: Boolean) {
        DebugUtil.d(TAG, "summaryFragmentScrollEnd select = $select")
        if (select) {
            summaryStopVisible()
        } else {
            summaryStopGone()
        }
    }

    override fun summaryFragmentScroll(positionOffset: Float) {
        if (summaryStopLayout?.isVisible == true) {
            summaryStopLayout?.alpha = positionOffset
        }
    }

    override fun isSummaryRunning(): Boolean {
        /*
        1，loading中（三个点的动画），
        2，流式返回中，
        3，数据已经完了，但是还在播放动画中
        这三个条件都属于要正在summary，需要界面做反馈
         */
        return viewModel?.isStartLoading() == true || viewModel?.isRunning() == true || summaryContentView?.isSummaryAnimator() == true
    }

    override fun setSummaryCallback(callback: IAISummaryCallback) {
        summaryCallback = callback
    }

    override fun setBottomMargin(marginBottom: Int) {
        DebugUtil.i(TAG, "setBottomMargin marginBottom = $marginBottom")
        this.insetsBottom = marginBottom
        summaryStopVisible()
    }

    override fun setPaddingBottom(paddingBottom: Int) {
        DebugUtil.i(TAG, "setPaddingBottom paddingBottom = $paddingBottom")
        this.paddingBottom = paddingBottom
        if (summaryContentView?.isSummaryAnimator() != true) {
            summaryStopGone()
        }
    }

    private fun summaryStopVisible() {
        val viewModel = viewModel ?: return
        DebugUtil.d(TAG, "summaryStopVisible, summaryState:${viewModel.summaryState.value}")
        val root = binding?.root ?: return
        val summaryStopLayout = this.summaryStopLayout ?: return
        val summaryContentView = this.summaryContentView ?: return
        if (summaryContentView.isSummaryAnimator()) {
            summaryStopLayout.alpha = 1f
            val constraintSet = ConstraintSet()
            constraintSet.clone(root)
            constraintSet.clear(summaryStopLayout.id, ConstraintSet.TOP)
            constraintSet.clear(summaryStopLayout.id, ConstraintSet.BOTTOM)
            constraintSet.clear(summaryContentView.id, ConstraintSet.BOTTOM)
            constraintSet.connect(summaryStopLayout.id, ConstraintSet.TOP, summaryContentView.id, ConstraintSet.BOTTOM, 0)
            constraintSet.connect(summaryStopLayout.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, insetsBottom)
            constraintSet.connect(summaryContentView.id, ConstraintSet.BOTTOM, summaryStopLayout.id, ConstraintSet.TOP, 0)
            constraintSet.applyTo(root)
            summaryStopLayout.visible()
        }
    }

    private fun summaryStopGone() {
        val viewModel = viewModel ?: return
        DebugUtil.d(TAG, "summaryStopGone, summaryState:${viewModel.summaryState.value}")
        summaryStopLayout?.gone()
        val root = binding?.root ?: return
        val summaryContentView = this.summaryContentView ?: return
        val constraintSet = ConstraintSet()
        constraintSet.clone(root)
        constraintSet.clear(summaryContentView.id, ConstraintSet.BOTTOM)
        val margin = if (viewModel.isStartLoading()) { insetsBottom } else { paddingBottom }
        constraintSet.connect(summaryContentView.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, margin)
        constraintSet.applyTo(root)
    }

    override fun onAttach(context: Context) {
        DebugUtil.d(TAG, "onAttach")
        super.onAttach(context)
        initBundleData()
    }

    private fun startOrResumeAISummary(activity: Activity?) {
        activity ?: return
        summaryContentView?.onStartLoading()
        viewModel?.loadSummary(mediaId, recordType, recordCreateTime)
    }

    private fun initBundleData() {
        val bundle = arguments ?: return
        mediaId = bundle.getLong(BUNDLE_MEDIA_ID)
        recordType = bundle.getInt(BUNDLE_RECORD_TYPE)
        recordCreateTime = bundle.getLong(BUNDLE_RECORD_MODIFY_TIME)
        recordTitle = bundle.getString(BUNDLE_RECORD_TITLE).toString()
        recordFilePath = bundle.getString(BUNDLE_RECORD_FILE_PATH).toString()
        DebugUtil.d(TAG, "initBundleData mediaId:$mediaId, recordType:$recordType, recordCreateTime:$recordCreateTime")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        DebugUtil.d(TAG, "onCreateView")
        val binding = LayoutSummaryFragmentBinding.bind(
            inflater.inflate(
                com.soundrecorder.summary.R.layout.layout_summary_fragment,
                container,
                false
            )
        )
        this.binding = binding
        viewModel = ViewModelProvider(this)[AISummaryViewModel::class.java]
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        DebugUtil.d(TAG, "onViewCreated")
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserver()
        startOrResumeAISummary(activity)
    }

    private fun initView() {
        summaryContentView = binding?.root?.findViewById(com.soundrecorder.summary.R.id.summary_content_view)
        summaryContentView?.setSummaryFunctionCallback(callback)
        // 设置录音文件信息，确保导出功能能正确获取录音文件信息
        summaryContentView?.setRecordInfo(mediaId, recordTitle, recordFilePath)
        summaryStopLayout = binding?.root?.findViewById(com.soundrecorder.summary.R.id.stop_layout)
        val summaryStopIcon = binding?.root?.findViewById<AppCompatImageView>(com.soundrecorder.summary.R.id.summary_stop)
        summaryStopIcon?.setOnClickListener {
            if (ClickUtils.isQuickClick()) {
                return@setOnClickListener
            }
            val summaryState = viewModel?.summaryState?.value
            DebugUtil.i(TAG, "stopSummary summaryState = $summaryState")
            when (summaryState) {
                AISummaryViewModel.SummaryState.START,
                AISummaryViewModel.SummaryState.TASK_ALREADY_RUN,
                AISummaryViewModel.SummaryState.WAIT,
                AISummaryViewModel.SummaryState.STREAM -> {
                    summaryContentView?.stopAnimator()
                    viewModel?.stopSummary(context)
                }

                AISummaryViewModel.SummaryState.FINISH,
                AISummaryViewModel.SummaryState.STOPPED -> {
                    currentChooseTheme = null
                    if (summaryContentView?.isSummaryAnimator() == true) {
                        summaryContentView?.stopAnimator()
                    } else {
                        summaryStopLayout?.gone()
                        summaryCallback?.onSummaryEnd()
                    }
                }

                else -> {
                    DebugUtil.i(TAG, "summaryState = $summaryState")
                    summaryStopLayout?.gone()
                    summaryCallback?.onSummaryEnd()
                }
            }
        }
        summaryStopGone()
    }

    private fun initObserver() {
        initSummaryStreamObserver()
        initSummaryFullModelObserver()
        initSummaryStateObserver()
        initSummaryCountObserver()
        initPermissionRequestObserver()
    }

    private fun checkFuncPrivacyStatement() {
        if (PermissionUtils.hasFuncTypePermission(BaseApplication.getAppContext(), FUNC_TYPE_SUMMARY_MIND).not()) {
            val functionPrivacy = privacyAction?.newFunctionPrivacyDelegate(FUNC_TYPE_SUMMARY_MIND)
            functionPrivacy?.showFunctionPrivacyDialog(
                requireActivity(),
                object : IFunctionPrivacyCallback {
                    override fun onPrivacyAgreed() {
                        viewModel?.resetPermissionRequestEvent()
                        AIUnitApi.startPrivacyGuide(requireActivity()) {
                            DebugUtil.d(TAG, "checkFuncPrivacyStatement result: $it")
                            if (it) {
                                viewModel?.continueSummary()
                            } else {
                                viewModel?.permissionDenied()
                            }
                        }
                    }

                    override fun onPrivacyRejected() {
                        viewModel?.permissionDenied()
                    }
                })
        }
    }


    private fun initSummaryStreamObserver() {
        viewModel?.summaryStream?.observe(viewLifecycleOwner) {
            summaryContentView?.updateStream(it.stream, animator = true, it.extra)
        }
    }

    private fun initSummaryFullModelObserver() {
        viewModel?.summaryFullModel?.observe(viewLifecycleOwner) {
            summaryContentView?.setSummaryContent(it)
            summaryStopGone()
        }
    }

    private fun initSummaryStateObserver() {
        viewModel?.summaryState?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSummaryStateObserver = $it")
            when (it) {
                AISummaryViewModel.SummaryState.START -> {
                    summaryContentView?.onStartSummary()
                    summaryStopGone()
                    summaryCallback?.onSummaryStart()
                }

                AISummaryViewModel.SummaryState.WAIT -> {
                    summaryContentView?.onStartSummary()
                    summaryStopGone()
                    summaryCallback?.onSummaryStart()
                }

                AISummaryViewModel.SummaryState.STREAM -> {
                    summaryCallback?.onSummaryStart()
                    summaryStopVisible()
                }

                AISummaryViewModel.SummaryState.TASK_ALREADY_RUN -> {
                    viewModel?.currentStream?.let { currentStream ->
                        summaryContentView?.updateStream(currentStream.stream, animator = false, currentStream.extra)
                    }
                    summaryCallback?.onSummaryStart()
                    summaryStopVisible()
                }

                AISummaryViewModel.SummaryState.OVER_TIME -> showWarningOverSummaryTimes()

                AISummaryViewModel.SummaryState.FINISH -> {
                    val summaryModel = viewModel?.summaryFinishModel ?: return@observe
                    summaryContentView?.onFinishSummary(summaryModel)
                }

                AISummaryViewModel.SummaryState.ERROR -> {
                    val summaryError = viewModel?.summaryError ?: return@observe
                    val msg = requireContext().getString(summaryError.errorMsgRes)
                    summaryContentView?.onError(summaryError.canRetry, msg)
                    summaryCallback?.onSummaryEnd()
                    summaryStopGone()
                }

                AISummaryViewModel.SummaryState.RETRY_ERROR -> {
                    val summaryError = viewModel?.summaryError ?: return@observe
                    val msg = requireContext().getString(summaryError.errorMsgRes)
                    ToastManager.showShortToast(context, msg)
                    summaryCallback?.onSummaryEnd()
                    summaryStopGone()
                    viewModel?.loadChooseSummary()
                }

                AISummaryViewModel.SummaryState.STOPPED -> {
                    currentChooseTheme = null
                    val reason = viewModel?.summaryStop?.reason ?: -1
                    when (reason) {
                        SummaryStop.REASON_STOP -> {
                            summaryCallback?.onSummaryEnd()
                            summaryStopGone()
                        }

                        else -> DebugUtil.d(TAG, "STOPPED reason = $reason")
                    }
                }

                else -> DebugUtil.e(TAG, "initSummaryStateObserver it = $it")
            }
        }
    }

    private fun initSummaryCountObserver() {
        viewModel?.summaryCount?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSummaryCountObserver = $it")
            val isLastSummary = it.currentPosition == it.count
            val isOnly = it.count == 1
            val isFirstSummary = it.currentPosition == 1
            summaryContentView?.checkCurrentState(isLastSummary, isOnly, isFirstSummary)
        }
    }

    private fun initPermissionRequestObserver() {
        viewModel?.apply {
            resetPermissionRequestEvent()
            permissionRequestEvent.observe(viewLifecycleOwner) { shouldShowDialog ->
                DebugUtil.d(TAG, "initPermissionRequestObserver shouldShowDialog = $shouldShowDialog")
                if (shouldShowDialog == true) {
                    checkFuncPrivacyStatement()
                }
            }
        }
    }

    private fun showWarningOverSummaryTimes() {
        val message = requireActivity().resources.getQuantityString(
            com.soundrecorder.common.R.plurals.summary_refresh_notice_dialog_msg,
            MAX_COUNT,
            MAX_COUNT
        )
        warningDialog = COUIAlertDialogBuilder(requireActivity())
            .setTitle(com.soundrecorder.common.R.string.summary_refresh_notice_dialog_title)
            .setMessage(message)
            .setPositiveButton(com.soundrecorder.common.R.string.summary_refresh_notice_dialog_yes) { dialog, _ ->
                if (currentChooseTheme != null) {
                    viewModel?.regenerateSummary(requireContext(), false, currentChooseTheme)
                } else {
                    viewModel?.regenerateSummary(requireContext(), false)
                }
                dialog.dismiss()
            }.setNeutralButton(com.soundrecorder.common.R.string.summary_refresh_notice_dialog_no) { dialog, _ ->
                dialog.dismiss()
            }.show()
    }

    override fun onStop() {
        super.onStop()
        DebugUtil.i(TAG, "onStop")
        //退出界面保存数据库
        viewModel?.updateSummaryAgentDB()
    }

    override fun onDetach() {
        super.onDetach()
        warningDialog?.dismiss()
        warningDialog = null
    }

    override fun onDestroy() {
        super.onDestroy()
        currentChooseTheme = null
    }
}