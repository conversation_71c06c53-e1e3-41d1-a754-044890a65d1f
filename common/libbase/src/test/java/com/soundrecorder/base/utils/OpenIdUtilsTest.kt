/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: OpenIdUtilsTest
 Description:
 Version: 1.0
 Date: 2022/7/28
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 --------------------Revision History: ---------------------
 <author> <date> <version> <desc>
 W9013333 2022/7/28 1.0 create
 */
package com.soundrecorder.base.utils

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.oplus.stdid.sdk.StdIDSDK
import com.soundrecorder.base.shadows.ShadowFeatureOption
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class OpenIdUtilsTest {
    private val mockStdIDSDK by lazy { Mockito.mockStatic(StdIDSDK::class.java) }

    @Test
    fun check_data() {
        mockStdIDSDK.`when`<Boolean> { StdIDSDK.isSupported() }.thenReturn(false)
        OpenIdUtils.INSTANCE.init(BaseApplication.getAppContext())
        Assert.assertNotEquals(OpenIdUtils.INSTANCE.uuid, "")
        Assert.assertEquals(OpenIdUtils.INSTANCE.duid, "")
        Assert.assertEquals(OpenIdUtils.INSTANCE.guid, "")
        Assert.assertEquals(OpenIdUtils.INSTANCE.ouid, "")
        mockStdIDSDK.`when`<Boolean> { StdIDSDK.isSupported() }.thenReturn(true)
        OpenIdUtils.INSTANCE.init(BaseApplication.getAppContext())
        Assert.assertNotEquals(OpenIdUtils.INSTANCE.uuid, "")
        Assert.assertNotEquals(OpenIdUtils.INSTANCE.duid, "")
        Assert.assertNotEquals(OpenIdUtils.INSTANCE.uuid, "")
        Assert.assertNotEquals(OpenIdUtils.INSTANCE.guid, "")
        Assert.assertNotEquals(OpenIdUtils.INSTANCE.ouid, "")
    }

    @After
    fun clear() {
        mockStdIDSDK.close()
    }
}