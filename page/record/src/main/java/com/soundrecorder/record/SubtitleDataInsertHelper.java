/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 * File: - SubtitleDataInsertHelper.java
 * Description:
 *     The helper class for ui process of subtitle data.
 * Version: 1.0
 * Date: 2025-06-06
 * Author: <EMAIL>
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-05-30   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import static com.soundrecorder.common.realtimeasr.AsrStatusConst.ERROR_NET_DISCONNECT;

import android.app.Activity;
import android.graphics.Rect;
import android.view.MotionEvent;
import android.view.View;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.oplus.anim.EffectiveAnimationView;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache;
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface;
import com.soundrecorder.modulerouter.utils.KoinInterfaceHelper;
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

public class SubtitleDataInsertHelper {

    private static final String TAG = "SubtitleDataInsertHelper";
    private static final int THRESHOLD = 5000;
    private static final int SETREQUESTCODE = 211;
    private int mLastFirstVisiblePosition = 0;
    private RenameFileDialog mRenameDialog = null;
    private boolean mIsLoadingSuccessful = false;
    private ConstraintLayout mCaptionsGradientView;
    private COUIRecyclerView mCaptionsRecyclerView;
    private LinearLayout mCaptionsLoadingView;
    private EffectiveAnimationView mCaptionsAnimationView;
    private RecordSubtitleAdapter mSubtitleAdapter;
    private WeakReference<Activity> mActivity;
    private List<ConvertContentItem> mTotalList = new ArrayList<>();
    private boolean mIsOpenSubtitles = false;
    private boolean mIsHoldOn = false;
    private RecorderServiceInterface mRecorderViewModelApi = KoinInterfaceHelper.INSTANCE.getRecorderViewModelApi();

    private Runnable mResetHoldOnRunnable = () -> {
        mIsHoldOn = false;
        if (mActivity == null || mSubtitleAdapter == null) {
            return;
        }
        Activity activity = mActivity.get();
        if (activity != null && !activity.isFinishing() && !activity.isDestroyed() && !mSubtitleAdapter.getDataList().isEmpty()) {
            mCaptionsRecyclerView.smoothScrollToPosition(mSubtitleAdapter.getDataList().size() - 1);
        }
    };

    public SubtitleDataInsertHelper(Activity context,
                                    ConstraintLayout mCaptionsGradientView,
                                    COUIRecyclerView mCaptionsRecyclerView,
                                    LinearLayout mCaptionsLoadingView,
                                    EffectiveAnimationView mCaptionsAnimationView) {
        this.mActivity = new WeakReference<>(context);
        this.mCaptionsGradientView = mCaptionsGradientView;
        this.mCaptionsRecyclerView = mCaptionsRecyclerView;
        this.mCaptionsLoadingView = mCaptionsLoadingView;
        this.mCaptionsAnimationView = mCaptionsAnimationView;
        initGradientRecyclerView();
        openSubtitles();
        initLoadingAnimation();
    }

    public void setAdapterData(List<DisplaySubtitleEntry> subtitles) {
        if (mActivity == null) {
            return;
        }
        Activity activity = mActivity.get();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }

        if (subtitles.isEmpty()) {
            showLoadingAnim(false);
            return;
        }
        if (mIsOpenSubtitles) {
            showLoadingAnim(true);
        }
        mActivity.get().runOnUiThread(() -> {
            DebugUtil.i(TAG, "setAdapterData mIsHoldOn: " + mIsHoldOn);
            mSubtitleAdapter.setData(subtitles);
            if (mSubtitleAdapter.getItemCount() - 1 > -1) {
                if (SubtitleRecyclerViewUtils.estimateTotalHeight(mCaptionsRecyclerView)
                        > SubtitleRecyclerViewUtils.getVisibleHeight(mCaptionsRecyclerView) && !mIsHoldOn) {
                    mCaptionsRecyclerView.smoothScrollToPosition(mSubtitleAdapter.getItemCount() - 1);
                }
            }
        });

    }

    public void onSubtitleUpdated(@NonNull IRealtimeSubtitleCache cache) {
        if (mActivity == null) {
            return;
        }
        Activity activity = mActivity.get();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        List<ConvertContentItem> completedItems = cache.getGeneratedSubtitles();
        List<ConvertContentItem> processingItem = cache.getTemporySubtitles();
        mTotalList.clear();
        DebugUtil.i(TAG, "onSubtitleUpdated: " + completedItems.size());
        if (completedItems.isEmpty() && processingItem.isEmpty()) {
            mActivity.get().runOnUiThread(() -> showLoadingAnim(false));
        } else {
            mTotalList.addAll(completedItems);
            mTotalList.addAll(processingItem);
            if (mIsOpenSubtitles) {
                mActivity.get().runOnUiThread(() -> showLoadingAnim(true));
            }
        }
    }

    public void onAsrError(int code) {
        DebugUtil.i(TAG, "onError: code=" + code);
        if (mSubtitleAdapter != null) {
            mSubtitleAdapter.setNoNetWork(code == ERROR_NET_DISCONNECT);
        }
    }

    /**
     * 录音转写功能默认关闭
     */
    private void openSubtitles() {
        openSubtitles(true);
    }

    /**
     * 录音转写功能开关
     *
     * @param isOpenSubtitles 是否开启
     */
    private void openSubtitles(boolean isOpenSubtitles) {
        mIsOpenSubtitles = isOpenSubtitles;
        if (mCaptionsGradientView == null) {
            return;
        }
        if (isOpenSubtitles) {
            mCaptionsGradientView.setVisibility(VISIBLE);
        } else {
            mCaptionsGradientView.setVisibility(GONE);
        }
    }

    private void initGradientRecyclerView() {
        DebugUtil.i(TAG, "=========>initGradientRecyclerView");
        if (mActivity == null) {
            return;
        }
        Activity activity = mActivity.get();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        mCaptionsRecyclerView.setLayoutManager(new LinearLayoutManager(mActivity.get()));
        mSubtitleAdapter = new RecordSubtitleAdapter(mActivity.get());
        mSubtitleAdapter.setListener(new RecordSubtitleAdapter.OnItemClickListener() {
            @Override
            public void onLongClickStop(int position) {
                DebugUtil.i(TAG, "onLongClickStop");
                mIsHoldOn = false;
            }

            @Override
            public void onItemLongClick(int position) {
                DebugUtil.i(TAG, "onItemLongClick");
                mIsHoldOn = true;
                mCaptionsRecyclerView.removeCallbacks(mResetHoldOnRunnable);
            }

            public void onItemClickSpeaker(int position) {
                DebugUtil.i(TAG, "onItemClickSpeaker");
                String name = mSubtitleAdapter.getDataList().get(position).getOriginContent().getRoleName();
                showRenameDialog(name == null ? "" : name,
                  mSubtitleAdapter.getDataList().get(position).getOriginContent().getRoleId());
            }
        });
        mCaptionsRecyclerView.setAdapter(mSubtitleAdapter);
        mSubtitleAdapter.setData(new ArrayList());
    }

    private void initLoadingAnimation() {
        if (mCaptionsAnimationView == null) {
            return;
        }
        mCaptionsAnimationView.setAnimation("ic_subtitle_loading.json");
        mCaptionsAnimationView.playAnimation();
    }

    public void showLoadingAnim(boolean isLoadingSuccessful) {
        mIsLoadingSuccessful = isLoadingSuccessful;
        if (mCaptionsRecyclerView == null || mCaptionsLoadingView == null) {
            DebugUtil.i(TAG, "showLoadingAnim");
            return;
        }
        if (mIsLoadingSuccessful) {
            mCaptionsRecyclerView.setVisibility(VISIBLE);
            mCaptionsLoadingView.setVisibility(GONE);
        } else {
            mCaptionsRecyclerView.setVisibility(GONE);
            mCaptionsLoadingView.setVisibility(VISIBLE);
        }
    }

    public void dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            if (isTouchPointInView(mCaptionsRecyclerView, (int) ev.getRawX(), (int) ev.getRawY())) {
                mIsHoldOn = true;
                mCaptionsRecyclerView.removeCallbacks(mResetHoldOnRunnable);
            }
        } else if (ev.getAction() == MotionEvent.ACTION_UP || ev.getAction() == MotionEvent.ACTION_CANCEL) {
            // 记录当前RecyclerView的滑动位置
            LinearLayoutManager layoutManager = (LinearLayoutManager) mCaptionsRecyclerView.getLayoutManager();
            if (layoutManager != null) {
                mLastFirstVisiblePosition = layoutManager.findFirstVisibleItemPosition();
                mCaptionsRecyclerView.postDelayed( mResetHoldOnRunnable, THRESHOLD);
            }
        }
    }

    private boolean isTouchPointInView(View view, int x, int y) {
        Rect rect = new Rect();
        view.getGlobalVisibleRect(rect);
        return rect.contains(x, y);
    }

    private void showRenameDialog(String text, long recordId) {
        if (mRenameDialog != null && mRenameDialog.isShowing()) {
            DebugUtil.i(TAG, "mRenameDialog is showing");
            return;
        }
        if (mActivity == null) {
            return;
        }
        Activity activity = mActivity.get();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }

        mRenameDialog = new RenameFileDialog(mActivity.get(), RenameFileDialog.FROM_PLAYBACK_MORE, text, (displayName, path) -> {
            mRecorderViewModelApi.updateSpeakerName(Integer.parseInt(recordId + ""), displayName);
            for (int i = 0; i < mSubtitleAdapter.getDataList().size(); i++) {
                if (recordId == mSubtitleAdapter.getDataList().get(i).getOriginContent().getRoleId()) {
                    mSubtitleAdapter.getDataList().get(i).getOriginContent().setRoleName(displayName);
                    mSubtitleAdapter.notifyItemChanged(i);
                    break;
                }
            }
        }
        );

        DebugUtil.i(TAG, "mRenameDialog != null text = ： " + text);
        mRenameDialog.setRequestCode(SETREQUESTCODE);
        mRenameDialog.show();
    }


    public void release() {
        if (mResetHoldOnRunnable != null) {
            mCaptionsRecyclerView.removeCallbacks(mResetHoldOnRunnable);
            mResetHoldOnRunnable = null;
            mSubtitleAdapter = null;
        }
        if (mActivity != null && mActivity.get() != null) {
            mActivity.clear();
            mActivity = null;
        }
        if (mCaptionsGradientView != null) {
            mCaptionsGradientView = null;
        }
        if (mCaptionsRecyclerView != null) {
            mCaptionsRecyclerView = null;
        }
        if (mCaptionsLoadingView != null) {
            mCaptionsLoadingView = null;
        }
        if (mCaptionsAnimationView != null) {
            mCaptionsAnimationView = null;
        }
        if (mRenameDialog != null) {
            mRenameDialog.dismiss();
            mRenameDialog = null;
        }
    }
}
