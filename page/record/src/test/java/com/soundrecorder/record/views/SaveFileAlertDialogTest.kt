/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SaveFileAlertDialogTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.record.views

import android.content.Context
import android.os.Build
import androidx.core.view.isVisible
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.coui.appcompat.checkbox.COUICheckBox
import com.soundrecorder.record.RecorderActivity
import com.soundrecorder.record.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.record.views.dialog.SaveFileAlertDialog
import com.soundrecorder.record.views.dialog.VerticalButtonDialogCallback
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
class SaveFileAlertDialogTest {

    private var controller: RecorderActivity? = null
    private var context: Context? = null

    @Before
    fun init() {
        controller =
            Robolectric.buildActivity(RecorderActivity::class.java).create().resume().get()
        context = ApplicationProvider.getApplicationContext<Context>()
    }

    private fun <T> any(type: Class<T>): T = Mockito.any<T>(type)

    @After
    fun tearDown() {
        controller = null
        context = null
    }

    @Test
    fun should_equals_when_isSummaryChecked() {
        val dialog = SaveFileAlertDialog("123", object : VerticalButtonDialogCallback {
            override fun save(displayName: String, originalDisplayName: String) {
                // do nothing
            }

            override fun disableAllClickViews(enable: Boolean) {
                // do nothing
            }

            override fun onCancel() {
                // do nothing
            }
        }, controller!!)
        val checkBox = COUICheckBox(context)
        checkBox.isChecked = false
        checkBox.isVisible = true
        Whitebox.setInternalState(dialog, "cbSummary", checkBox)
        Assert.assertFalse(dialog.isSummaryChecked())
        Assert.assertTrue(dialog.isSummaryVisible())
    }

    @Test
    fun should_equals_when_getOriginalContent() {
        val content = "123"
        val dialog = SaveFileAlertDialog(content, object : VerticalButtonDialogCallback {
            override fun save(displayName: String, originalDisplayName: String) {
                // do nothing
            }

            override fun disableAllClickViews(enable: Boolean) {
                // do nothing
            }

            override fun onCancel() {
                // do nothing
            }
        }, controller!!)
        Assert.assertEquals(content, dialog.getOriginalContent())
    }
}