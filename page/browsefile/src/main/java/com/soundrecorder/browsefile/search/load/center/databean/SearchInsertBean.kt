package com.soundrecorder.browsefile.search.load.center.databean

import android.database.Cursor
import android.provider.MediaStore
import androidx.annotation.Keep
import com.soundrecorder.base.ext.title
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils

@Keep
data class SearchInsertBean(
    var bucket: String = "",
    var media_path: String = "",
    var text_path: String = "",
    var id: Long = 0,
    // 不含文件后缀，不匹配文件后缀
    var display_name: String = "",
    var size: Long = 0,
    var date_modified: Long = 0,
    var duration: Long = 0,
    // 扩展字段，在中子不改动的情况下，仅展示所用
    // 目前里面含mimeType，存的是json字符串
    // 该字段若改动，需同步修改 SearchCenterProvider.query
    var extend: String? = "",
    @Suppress("ConstructorParameterNaming") var summary_text_flag: Int = 0
) {

    /**
     * 统一extend 扩展字段封装类
     */
    @Transient
    var tempExtendBean: SearchInsertExtendBean? = null

    constructor(cursor: Cursor) : this() {
        val idIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns._ID)
        if (idIndex >= 0) {
            id = cursor.getLong(idIndex)
        }
        val dataIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATA)
        if (dataIndex >= 0) {
            media_path = cursor.getString(dataIndex)
        }
        val sizeIndex = cursor.getColumnIndex(MediaStore.Audio.Media.SIZE)
        if (sizeIndex >= 0) {
            size = cursor.getLong(sizeIndex)
        }
        val displayNameIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DISPLAY_NAME)
        if (displayNameIndex >= 0) {
            display_name = cursor.getString(displayNameIndex).title() ?: ""
        }
        val modifyTimeIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DATE_MODIFIED)
        if (modifyTimeIndex >= 0) {
            date_modified = cursor.getLong(modifyTimeIndex)
        }
        val durationIndex = cursor.getColumnIndex(MediaStore.Audio.Media.DURATION)
        if (durationIndex >= 0) {
            duration = cursor.getLong(durationIndex)
        }

        val relativePathIndex = cursor.getColumnIndex(MediaStore.Video.VideoColumns.RELATIVE_PATH)
        if (relativePathIndex >= 0) {
            val relativePath = cursor.getString(relativePathIndex)
            CenterDbUtils.calBucketByRelativePath(relativePath)?.let { bucket = it }
        }

        packageExtendValue(cursor)
    }

    fun packageExtendValue(cursor: Cursor) {
        tempExtendBean = SearchInsertExtendBean().apply {
            val mimeTypeIndex = cursor.getColumnIndex(MediaStore.Audio.Media.MIME_TYPE)
            if (mimeTypeIndex != -1) {
                this.mimeType = cursor.getString(mimeTypeIndex)
            }

            extend = this.toJsonString()
        }
    }
}