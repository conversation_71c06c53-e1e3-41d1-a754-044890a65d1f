/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BaseRecord
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/6/4
 * * Author      : tianjun
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.item

open class BaseItemRecordViewModel(
    var title: String? = null,
    var displayName: String? = null,
    var data: String? = null,
    var relativePath: String? = null,
    var ownerPackageName: String? = null,
    var mDuration: Long = 0,
    var dateModified: Long = 0,
    var mediaId: Long = 0,
    var taskId: Int = 0,
    var size: Long = 0,
    var mimeType: String? = null,
    var globalId: String? = null,
    var isRecycle: Boolean = false,
    var recyclePath: String? = null,
    var deleteTime: Long = 0,
    var groupUuid: String? = null,
    var groupId: Int = -1,
    var callerName: String? = null,
    var originalName: String? = null,
)