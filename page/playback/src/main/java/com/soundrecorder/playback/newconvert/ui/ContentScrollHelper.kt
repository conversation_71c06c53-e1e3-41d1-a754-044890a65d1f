/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui

import android.content.Context
import android.graphics.Rect
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.R
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.view.BackgroundTextView
import com.soundrecorder.playback.newconvert.view.CustomLinearLayoutManager

class ContentScrollHelper(
    private val mContainer: ViewGroup?,
    private var mViewModel: PlaybackActivityViewModel?,
    private var mConvertViewModel: PlaybackConvertViewModel?,
    private val mConvertContent: COUIRecyclerView?,
) {
    //是否是用户的行为导致的列表滚动
    var mIsFromUser: Boolean = false

    var mContext: Context? = mContainer?.context

    //是否需要展示新手提示（“点击可编辑讲话人姓名”），列表每次滚动的时候需要
    private var mNeedShowRoleName: Boolean = false
    private val mConvertContentAdapter: TextImageItemAdapter? =
        mConvertContent?.adapter as? TextImageItemAdapter
    private val mLinearLayoutManager: CustomLinearLayoutManager? =
        mConvertContent?.layoutManager as? CustomLinearLayoutManager

    private var mMeasureTextView: BackgroundTextView? = null

    //触发切换沉浸态第一段动画可滚动的距离
    private val immersiveFirstMoveDownDistance: Float by lazy {
        mContext?.resources?.getDimension(R.dimen.distance_immersive_first_move_down) ?: 0f
    }

    //触发切换沉浸态列表滚动的距离
    private val switchImmersiveStateDistance: Int by lazy {
        mContext?.resources?.getDimension(R.dimen.distance_switch_immersive)?.toInt()
            ?: Int.MAX_VALUE
    }

    //沉浸态第一段动画操作栏下移距离与列表实际滚动距离的比率
    private val moveDownDistanceRatio =
        immersiveFirstMoveDownDistance / switchImmersiveStateDistance

    companion object {
        const val TAG = "ContentScrollHelper"
        const val NUM_TEN = 10
        const val NUM_FIVE = 5
        const val NUM_ONE = 1
    }


    /**
     * 根据播放时间更新高亮位置
     * 1.找到上一个高亮的位置
     * 2.找到当前需要高亮的位置
     * 3.重置上一个高亮位置
     * 4，开始滚动
     * 5.高亮并且记录当前位置
     *
     * @param currentTimeMillis 当前的播放时间
     */

    fun update(currentTimeMillis: Long) {
        updateBySentence(currentTimeMillis)
    }


    /**
     * 开始滚动之前的准备
     * 清除上个高亮位置的状态值
     * 记录当前高亮位置的状态值
     * @param focusPositionCursor 当前需要高亮的位置
     * @param lastFocusedItem 上一个高亮的item
     */
    private fun prepareToScroll(
        list: List<Pair<Int, ConvertContentItem.TextItemMetaData>>,
        focusPositionCursor: Int,
        focusSubPositionCursor: Int,
        lastFocusedItem: ConvertContentItem.TextItemMetaData,
    ) {

        for (sentence in lastFocusedItem.textParagraph!!) {
            sentence.isFocused = false
        }
        val currentFocusItem = getTextItemMetaDataForPosition(list, focusPositionCursor)
        val currentSentence = currentFocusItem?.textParagraph?.get(focusSubPositionCursor)
        DebugUtil.i(TAG, "prepareToScroll focusPositionCursor=$focusPositionCursor, " +
                "currentFocusItem $currentFocusItem, currentSentence $currentSentence, set focus true")
        currentSentence?.isFocused = true
    }

    private fun prepareToScrollForTime(
        list: List<Pair<Int, ConvertContentItem.TimerDividerMetaData>>,
        focusPositionCursor: Int,
        lastFocusedItem: ConvertContentItem.TimerDividerMetaData
    ) {
        val currentFocusItem = getTimeItemMetaDataForPosition(list, focusPositionCursor)
        if (lastFocusedItem.startTime != currentFocusItem?.startTime) {
            lastFocusedItem.focused = false
        }
        DebugUtil.i(TAG, "prepareToScrollForTime focusPosition=$focusPositionCursor, currentFocusItem $currentFocusItem")
        currentFocusItem?.focused = true
    }


    private fun startToScroll(currentTimeMillis: Long, focusPositionCursor: Int, offset: Int = 0) {
        if (mViewModel?.mIsPageStopScroll?.value != false) {
            return
        }
        if (mConvertViewModel?.mHasUserDraggedText?.value != false && mViewModel?.needSyncScrollConvertContentView == false) {
            return
        }
        startToScrollInternal(currentTimeMillis, focusPositionCursor, offset)
    }

    private fun startToScrollInternal(
        currentTimeMillis: Long,
        focusPositionCursor: Int,
        offset: Int = 0
    ) {
        val headerSize = mConvertContentAdapter?.getHeaderSize() ?: 0
        mViewModel?.needSyncScrollConvertContentView = false
        if (((focusPositionCursor + headerSize) < mConvertContentAdapter?.itemCount ?: 0)
        ) { // !mIsDragging
            mConvertContent?.stopScroll()
            //滚动动画的倍数，当大于了10的时候不使用动画
            resetSmoothMultiple(focusPositionCursor)
            /**
             *  when isTouching ProgressBar does not do animator
             *  when multiple is too big , does not do animator; because it can not smoothScrollTo right Position
             *  用户正在拖动seekBar，则不需要滚动动画；
             *  当前距离和目标距离太远，则不需要动画。
             */
            mConvertContent?.post {
                if (mContainer?.parent != null) {
                    DebugUtil.i(TAG, "startToScrollInternal currentTimeMillis $currentTimeMillis focusPositionCursor $focusPositionCursor}")
                    if ((currentTimeMillis < NumberConstant.NUM_L1000)) {
                        mLinearLayoutManager?.scrollToPositionWithOffset(0, 0)
                    } else if ((mViewModel?.playerController?.mIsTouchSeekbar?.value == true) ||
                        (mLinearLayoutManager?.multiple ?: NUM_ONE > NUM_TEN)
                    ) {
                        mLinearLayoutManager?.scrollToPositionWithOffset(
                            focusPositionCursor + headerSize,
                            (mContainer?.height ?: 0) / NUM_FIVE - offset
                        )
                    } else {
                        mLinearLayoutManager?.offset = offset
                        mConvertContent.smoothScrollToPosition(focusPositionCursor + headerSize)
                    }
                }
            }
        }
    }


    //刷新并记录高亮位置
    private fun refreshBackGround(focusPositionCursor: Int, focusSubPositionCursor: Int) {
        mViewModel?.mLastFocusPosition = focusPositionCursor
        mViewModel?.mLastSubFocusPosition = focusSubPositionCursor
        updateContentBackButton(false)
        mConvertContentAdapter?.refreshAllViewHolderContentBackground()
    }

    /**
     * 根据播放时间找到需要高亮位置的position
     * 播放进度在上个item之后：
     * 从（mLastFocusPosition + 1 ）开始往后找，如果下一个item的开始时间小于当前的播放时间，则记录位置，继续找
     * 直到找到某个item的时间大于当前播放时间，则退出循环，前一个item的position就是需要
     *
     *
     * 播放位置在上个item之前：
     * 从（mLastFocusPosition + 1 ）开始往前找，直到找到前一个item的开始时间小于当前播放时间
     *
     * @param lastFocusItem 上一个高亮item
     * @return 需要高亮位置的position
     */
    private fun findFocusPositionCursor(
        list: List<Pair<Int, ConvertContentItem.TextItemMetaData>>,
        currentTimeMillis: Long,
        lastFocusItem: ConvertContentItem.TextItemMetaData,
    ): Int {
        val mLastFocusPosition = mViewModel?.mLastFocusPosition
        var focusPositionCursor = mLastFocusPosition ?: -1
        if ((list.isEmpty())) {
            //这里列表为空时返回默认值为position最初都是1，第0个为TimeDivider类型，所以默认值为1（表明时第一个），理论上这个分支走不进来
            return 1
        }
        //当前时间小于第一个item的起始时间 焦点段落设为第一个段落
        val firstSentenceStartTime = list[0].second.getBeginTime()
        /*
        DebugUtil.i(TAG, "findFocusPositionCursor begin firstSentenceStartTime $firstSentenceStartTime" +
                    ", currentTimeMillis $currentTimeMillis, focusPositionCursor $focusPositionCursor list ${list.size}")
         */
        if (currentTimeMillis < firstSentenceStartTime) {
            //这里列表为不为空时，时间小于第一个分段时间，返回第一个分段的position
            return list.first().first
        }

        val listIndex = getListIndexForPosition(list, focusPositionCursor)
        if (currentTimeMillis >= lastFocusItem.getBeginTime()) {
            //播放进度在lastFocusItem之后
            val newIndex = if (listIndex < 0) 0 else listIndex + 1
            for (i in newIndex until list.size) {
                val nextItem = list[i]
                if (currentTimeMillis >= nextItem.second.getBeginTime()) {
                    focusPositionCursor = nextItem.first
                } else {
                    break
                }
            }
        } else {
            //播放位置在lastFocusItem之前
            val newIndex = if (listIndex < 0) list.size - 1 else listIndex
            for (i in newIndex downTo 0) {
                val nextItem = list[i]
                if (currentTimeMillis >= nextItem.second.getBeginTime()) {
                    focusPositionCursor = nextItem.first
                    break
                }
            }
        }
        //DebugUtil.i(TAG, "findFocusPositionCursor end , firstSentenceStartTime ${firstSentenceStartTime}, listIndex $listIndex, focusPositionCursor $focusPositionCursor , ")
        return focusPositionCursor
    }

    //找到上一个高亮位置
    private fun getLastFocusItem(list: List<Pair<Int, ConvertContentItem.TextItemMetaData>>): ConvertContentItem.TextItemMetaData {
        val lastFocusPostion = mViewModel?.mLastFocusPosition ?: -1
        return if (lastFocusPostion != -1) {
            getTextItemMetaDataForPosition(list, lastFocusPostion) ?: list.first().second
        } else {
            list.first().second
        }
    }

    private fun getLastFocusTimeItem(list: List<Pair<Int, ConvertContentItem.TimerDividerMetaData>>): ConvertContentItem.TimerDividerMetaData {
        val lastFocusPostion = mViewModel?.mLastFocusTimePosition ?: -1
        return if (lastFocusPostion != -1) {
            getTimeItemMetaDataForPosition(list, lastFocusPostion) ?: list.first().second
        } else {
            list.first().second
        }
    }

    private fun getTextItemMetaDataForPosition(
        list: List<Pair<Int, ConvertContentItem.TextItemMetaData>>,
        position: Int
    ): ConvertContentItem.TextItemMetaData? {
        val resultPair = list.find { pair -> pair.first == position }
        //DebugUtil.i(TAG, "getTextItemMetaDataForPosition position $position, resultPair $resultPair")
        return resultPair?.second
    }

    private fun getTimeItemMetaDataForPosition(
        list: List<Pair<Int, ConvertContentItem.TimerDividerMetaData>>,
        position: Int
    ): ConvertContentItem.TimerDividerMetaData? {
        val resultPair = list.find { pair -> pair.first == position }
        //DebugUtil.i(TAG, "getTimeItemMetaDataForPosition position $position, resultPair $resultPair")
        return resultPair?.second
    }

    private fun findFocusTimeItemPosition(
        list: List<Pair<Int, ConvertContentItem.TimerDividerMetaData>>,
        currentTimeMillis: Long,
        lastFocusItem: ConvertContentItem.TimerDividerMetaData
    ): Int {
        val mLastFocusPosition = mViewModel?.mLastFocusTimePosition
        var focusPositionCursor = mLastFocusPosition ?: -1
        if ((list.isEmpty())) {
            return 0
        }
        //当前时间小于第一个item的起始时间 焦点段落设为第一个段落
        val firstTime = list[0].second.startTime
        /*
        DebugUtil.i(TAG, "findFocusTimeItemPosition begin firstTime $firstTime" +
                ", currentTimeMillis $currentTimeMillis, focusPositionCursor $focusPositionCursor list ${list.size}")
         */
        if (currentTimeMillis < firstTime) {
            //这里列表为不为空时，时间小于第一个分段时间，返回第一个分段的position
            return list.first().first
        }
        val listIndex = getTimeListIndexForPosition(list, focusPositionCursor)
        if (currentTimeMillis >= lastFocusItem.startTime) {
            //播放进度在lastFocusItem之后
            val newIndex = if (listIndex < 0) 0 else listIndex + 1
            for (i in newIndex until list.size) {
                val nextItem = list[i]
                if (currentTimeMillis >= nextItem.second.startTime) {
                    focusPositionCursor = nextItem.first
                } else {
                    break
                }
            }
        } else {
            //播放位置在lastFocusItem之前
            val newIndex = if (listIndex < 0) list.size - 1 else listIndex
            for (i in newIndex downTo 0) {
                val nextItem = list[i]
                if (currentTimeMillis >= nextItem.second.startTime) {
                    focusPositionCursor = nextItem.first
                    break
                }
            }
        }

        return focusPositionCursor
    }

    /**
     * 根据上次高亮的position，在纯文本的列表中查找位置，position和index不同
     * 例如time+text1+text2+image,time+text3+image，position=2(text2),需要返回1（text1,text2,text3中test2的index）
     * @param list 代表纯文本列表（text1,text2,text3）
     */
    private fun getListIndexForPosition(
        list: List<Pair<Int, ConvertContentItem.TextItemMetaData>>,
        position: Int
    ): Int {
        var resultIndex = -1
        run breaking@{
            list.forEachIndexed { index, pair ->
                if (pair.first == position) {
                    resultIndex = index
                    return@breaking
                }
            }
        }
        //DebugUtil.i(TAG, "getListIndexForPosition position $position, resultPair $resultIndex")
        return resultIndex
    }


    private fun getTimeListIndexForPosition(
        list: List<Pair<Int, ConvertContentItem.TimerDividerMetaData>>,
        position: Int
    ): Int {
        var resultIndex = -1
        run breaking@{
            list.forEachIndexed { index, pair ->
                if (pair.first == position) {
                    resultIndex = index
                    return@breaking
                }
            }
        }
        //DebugUtil.i(TAG, "getTimeListIndexForPosition position $position, resultPair $resultIndex")
        return resultIndex
    }

    /**
     * To change scroll speed ; If scroll distance is longer, the speed is faster;
     * Scroll distance is infacted by two factors:
     * 1 FocusPosition
     * 2 FirstVisibleItemPosition
     */

    private fun resetSmoothMultiple(focusPositionCursor: Int) {
        val tempMultiple1 = Math.abs((mViewModel?.mLastFocusPosition ?: -1) - focusPositionCursor) / 4
        val pos = mLinearLayoutManager?.findFirstVisibleItemPosition()
        val tempMultiple2 = Math.abs((pos ?: -1) - focusPositionCursor) / 4
        mLinearLayoutManager?.multiple = Math.max(tempMultiple1, tempMultiple2)
    }


    fun updateBySentence(currentTimeMillis: Long) {
        mConvertViewModel?.timeItems?.let {
            if (it.isEmpty()) return
            val lastFocusItem = getLastFocusTimeItem(it)
            //DebugUtil.i(TAG, "updateBySentence getLastFocusTimeItem $lastFocusItem")
            val focusPositionCursor = findFocusTimeItemPosition(it, currentTimeMillis, lastFocusItem)
            if (focusPositionCursor < 0) {
                DebugUtil.e(TAG, "=====>findFocusTimeItemPosition: $focusPositionCursor is illegal!")
                return
            }
            if (mViewModel?.mLastFocusTimePosition != focusPositionCursor) {
                prepareToScrollForTime(it, focusPositionCursor, lastFocusItem)
                mViewModel?.mLastFocusTimePosition = focusPositionCursor
            }
        }
        mConvertViewModel?.mTextItems?.let {
            if (it.isEmpty()) return

            val lastFocusItem = getLastFocusItem(it)
            //DebugUtil.i(TAG, "updateBySentence lastFocusItem $lastFocusItem")
            val focusPositionCursor = findFocusPositionCursor(it, currentTimeMillis, lastFocusItem)
            if (focusPositionCursor < 0) {
                DebugUtil.e(TAG, "=====>focusPositionCursor: $focusPositionCursor is illegal!")
                return
            }

            val focusSubPositionCursor = findSubFocusPositionCursor(it, currentTimeMillis, focusPositionCursor)
            if (focusSubPositionCursor < 0) {
                DebugUtil.e(TAG, "=====>focusSubPositionCursor: $focusSubPositionCursor is illegal!")
                return
            }

            if (mViewModel?.mLastFocusPosition != focusPositionCursor
                || mViewModel?.mLastSubFocusPosition != focusSubPositionCursor
                || mViewModel?.needSyncScrollConvertContentView == true
                || currentTimeMillis == NumberConstant.NUM_L0
            ) {
                val currentItem = getTextItemMetaDataForPosition(it, focusPositionCursor)
                val adapterDataPosition = findAdapterDataPosition(focusPositionCursor, currentItem)
                prepareToScroll(it, focusPositionCursor, focusSubPositionCursor, lastFocusItem)
                refreshBackGround(focusPositionCursor, focusSubPositionCursor)
                //todo 这个地方需要宇哥确认一下，宇哥有修改
                val stringBeforeFocus = currentItem?.getTextStringBeforeIndex(focusSubPositionCursor)
                var offset = 0
                if (stringBeforeFocus != null) {
                    offset = getScrollOffSet(stringBeforeFocus, stringBeforeFocus.length)
                }
               // DebugUtil.i(TAG, "updateBySentence offset $offset")
                startToScroll(currentTimeMillis, adapterDataPosition, offset)
            }
        }
    }

    /**
     * @param allDataFocusPosition 这个是全部数据的position
     * @param item 通过allDataFocusPosition找到的具体数据item
     *
     * @return 对于adapter data的position
     */
    private fun findAdapterDataPosition(allDataFocusPosition: Int, item: ConvertContentItem.TextItemMetaData?): Int {
        if (item != null && mConvertContentAdapter?.mContentItemList?.size != mConvertViewModel?.convertContentData?.size) {
            mConvertContentAdapter?.data?.forEachIndexed { index, itemMetaData ->
                if (itemMetaData is ConvertContentItem.TextItemMetaData && item.getBeginTime() == itemMetaData.getBeginTime()) {
                    return index
                }
            }
        }
        return allDataFocusPosition
    }

    private fun findSubFocusPositionCursor(
        list: List<Pair<Int, ConvertContentItem.TextItemMetaData>>,
        currentTimeMillis: Long,
        focusPositionCursor: Int,
    ): Int {
        //DebugUtil.i(TAG, "findSubFocusPositionCursor currentTimeMillis $currentTimeMillis, focusPositionCursor $focusPositionCursor")
        val item = getTextItemMetaDataForPosition(list, focusPositionCursor)
        if (item?.textParagraph == null) {
            DebugUtil.e(TAG, "=====> listSubSentence is null!")
            return -1
        }

        if (item.textParagraph?.size == 1) {
            return 0
        }
        for (i in item.textParagraph!!.indices) {
            if (i == item.textParagraph!!.size - 1) {
                return i
            }
            if (currentTimeMillis < item.textParagraph!![i + 1].time) {
                return i
            }
        }
        return 0
    }


    /**
     * 重建或者倒计时结束的时候手动更新高亮位置
     */
    fun scrollToLastPositionByManual() {
        hideBackButtonValue()
        scrollToLastPositionByManualSentence()
    }

    /**
     * 手动更新气泡显示
     */
    fun hideBackButtonValue() {
        mConvertViewModel?.mHasUserDraggedText?.value = false
    }


    /**
     * 重建或者倒计时结束的时候手动更新高亮位置
     */
    private fun scrollToLastPositionByManualSentence() {
        val lastFocusPosition = mViewModel?.mLastFocusPosition ?: -1
        val lastFocusSubPosition = mViewModel?.mLastSubFocusPosition ?: -1
        if (lastFocusPosition < 0) {
            DebugUtil.e(TAG, "=====>lastFocusPosition: $lastFocusPosition is illegal!")
            return
        }
        if (lastFocusSubPosition < 0) {
            DebugUtil.e(TAG, "=====>lastFocusSubPosition: $lastFocusSubPosition is illegal!")
            return
        }
        mConvertViewModel?.mTextItems?.let {
            if (it.isEmpty()) return
            val lastConvertContentItem = getTextItemMetaDataForPosition(it, lastFocusPosition)
            val adapterFocusPosition = findAdapterDataPosition(lastFocusPosition, lastConvertContentItem)
            if (adapterFocusPosition < 0) {
                DebugUtil.e(TAG, "=====>adapterFocusPosition: $adapterFocusPosition is illegal!")
                return
            }
            refreshBackGround(lastFocusPosition, lastFocusSubPosition)
            val stringBeforeFocus = lastConvertContentItem?.getTextStringBeforeIndex(lastFocusSubPosition)
            var offset = 0
            if (stringBeforeFocus != null) {
                offset = getScrollOffSet(stringBeforeFocus, stringBeforeFocus.length)
            }
            /*
            DebugUtil.i(TAG, "scrollToLastPositionByManualSentence offset $offset" +
                    ", mLastFocusPosition $lastFocusPosition, lastFocusSubPosition $lastFocusSubPosition,adapterFocusPosition=$adapterFocusPosition")
             */
            // real position
            startToScrollInternal(
                lastConvertContentItem?.getBeginTime() ?: 0,
                adapterFocusPosition,
                offset
            )
        }
    }

    fun restoreForRebuild() {
        mConvertContent?.post {
            //DebugUtil.i(TAG, "restoreForRebuild realstart")
            scrollToLastPositionByManual()
        }
    }


    /**
     * 转文本列表滚动监听。
     */
    private var mOnScrollListener: RecyclerView.OnScrollListener? = object : RecyclerView.OnScrollListener() {

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            when (newState) {

                /**
                 * 是用户行为导致的正在拖动状态
                 */
                RecyclerView.SCROLL_STATE_DRAGGING -> {
                    mIsFromUser = true
                }
                /**
                 * 手指离开页面后即切换mIsFromUser状态
                 */
                RecyclerView.SCROLL_STATE_SETTLING -> {
                    updateContentBackButton(false)
                    if (mIsFromUser) {
                        mIsFromUser = false
                    }
                }
                /**
                 * 手指慢慢离开页面 SCROLL_STATE_SETTLING 可能不会回调
                 */
                RecyclerView.SCROLL_STATE_IDLE -> {
                    updateContentBackButton(false)
                    if (mIsFromUser) {
                        mIsFromUser = false
                    }
                }
            }
        }

        /**
         * 在滑动到顶之后显示讲话人新手提示
         * mNeedShowRoleName 需要展示讲话人新手提示（“点击可编辑讲话人姓名）
         * canScrollVertically(-1) 列表已经滚动到顶了
         */
        override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
            super.onScrolled(recyclerView, dx, dy)
            if (mIsFromUser) {
                updateContentBackButton(true)
                updateIsImmersiveState(dy)
            }
            if (mNeedShowRoleName && !recyclerView.canScrollVertically(-1)) {
                //DebugUtil.d(TAG, "onScrolled, recyclerView canScrollVertically: false")
                recyclerView.stopScroll()
                mViewModel?.mIsPageStopScroll?.value = true
            }
        }
    }

    /**
     * 用户滑动手势监听
     */
    private val gestureDetector = GestureDetector(mContext, object : GestureDetector.SimpleOnGestureListener() {
            override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
                val canScroll = mConvertContent?.canScrollVertically(-1) ?: true
                if (distanceY < 0 && !canScroll) {   // 用户尝试向上滑动，但视图不能滚动
                    updateIsImmersiveState(distanceY.toInt())
                }
                return false
            }

            override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
                return false
            }
        })

    /**
     * 显示讲话人新手提示
     * 停止上下滚动
     */
    fun stopScrollForce() {
        mLinearLayoutManager?.scrollToPositionWithOffset(0, 0)
        mViewModel?.mIsPageStopScroll?.value = true
    }

    fun setNeedShowRoleName(needShowRole: Boolean) {
        mNeedShowRoleName = needShowRole
    }

    fun addOnScrollListener() {
        mOnScrollListener?.let { mConvertContent?.addOnScrollListener(it) }
        addOnTouchListener()
        DebugUtil.i(TAG, "addOnScrollListener mOnScrollListener=$mOnScrollListener")
    }

    private fun addOnTouchListener() {
        mConvertContent?.setOnTouchListener { _, event ->
            handleTouchEvent(event)
            false // 返回false，保证不影响原有的事件分发
        }
    }

    private fun handleTouchEvent(event: MotionEvent): Boolean {
        gestureDetector.onTouchEvent(event) // 委托给GestureDetector
        return false
    }

    fun removeOnScrollListener() {
        mOnScrollListener?.let { mConvertContent?.removeOnScrollListener(it) }
        mOnScrollListener = null
        DebugUtil.i(TAG, "removeOnScrollListener mOnScrollListener=$mOnScrollListener")
    }


    fun release() {
        mOnScrollListener = null
    }

    fun setViewModel(viewModel: PlaybackActivityViewModel?) {
        mViewModel = viewModel
    }

    fun setConvertViewModel(viewModel: PlaybackConvertViewModel?) {
        mConvertViewModel = viewModel
    }

    fun getOrInitMeasureTextView(): BackgroundTextView? {
        if (mMeasureTextView == null) {
            mMeasureTextView = mContainer?.findViewById(R.id.measureTextView)
        }
        return mMeasureTextView
    }

    fun getScrollOffSet(textContent: String, startSeq: Int): Int {
        getOrInitMeasureTextView()
        mMeasureTextView?.text = textContent
        val line = mMeasureTextView?.layout?.getLineForOffset(startSeq) //获取字符在第几行
        val bound = Rect()
        line?.let { mMeasureTextView?.layout?.getLineBounds(it, bound) }
        val charY = bound.top
        //DebugUtil.i(TAG, "====>charY = $charY;  startSeq = $startSeq")
        mMeasureTextView?.text = ""
        return charY
    }

    /**
     * 更新返回播放气泡显示与箭头方向
     */
    fun updateContentBackButton(updateShow: Boolean = false) {
        DebugUtil.d(TAG, "updateContentBackButton updateShow == $updateShow")
        mConvertContent?.post {
            val pair = getLocationBackShowAndDown()
            DebugUtil.d(
                TAG,
                "updateContentBackButton first = ${pair.first}, second = ${pair.second}"
            )
            if ((updateShow) && (mConvertViewModel?.mHasUserDraggedText?.value != pair.first)) {
                mConvertViewModel?.mHasUserDraggedText?.value = pair.first
            }
            if (mConvertViewModel?.mHasUserDraggedText?.value != true) {
                DebugUtil.d(TAG, "updateContentBackButton mHasUserDraggedText is same return")
                return@post
            }
            if (mConvertViewModel?.mContentBackDirection?.value != pair.second) {
                mConvertViewModel?.mContentBackDirection?.value = pair.second
            }
        }
    }

    /**
     * 获取转文本回到当前播放位置气泡的显示与箭头指向
     * 判断当前是否显示气泡：通过mViewModel中存的播放位置,与当前转文本屏幕1/5进行高度比较
     * 判断箭头：通过mViewModel中存的播放位置,与当前visiblePosition进行比较,visible位置小于播放位置,则箭头向下
     */
    private fun getLocationBackShowAndDown(): Pair<Boolean, Boolean> {
        var shouldShow = false
        var directionDown = false
        val layoutManager = mConvertContent?.layoutManager as? LinearLayoutManager
        val firstVisibleItem = layoutManager?.findFirstVisibleItemPosition()
        val lastVisibleItem = layoutManager?.findLastVisibleItemPosition()
        var lastFocusPosition = mViewModel?.mLastFocusPosition ?: 0
        if (lastFocusPosition < 0) {
            lastFocusPosition = 0
        }
        var lastSubFocusPosition = mViewModel?.mLastSubFocusPosition ?: 0
        if (lastSubFocusPosition < 0) {
            lastSubFocusPosition = 0
        }
        val viewHolder = mConvertContent?.findViewHolderForLayoutPosition(lastFocusPosition + 1)
        mConvertViewModel?.mTextItems?.let {
            val lastFocusItem = getTextItemMetaDataForPosition(it, lastFocusPosition)
            val stringBeforeFocus = lastFocusItem?.getTextStringBeforeIndex(lastSubFocusPosition)
            var offset = 0
            if (stringBeforeFocus != null) {
                offset = getScrollOffSet(stringBeforeFocus, stringBeforeFocus.length)
            }
            val itemTop = (viewHolder?.itemView?.top ?: 0) + offset
            val baseHeight = (mConvertContent?.height ?: 0) / 5
            shouldShow = if (itemTop > baseHeight) {
                /*目标在当前上方，需整体往上滚动，判断recycleView是否滚到底了，如果已经滚到底，则不显示气泡*/
                mConvertContent?.canScrollVertically(1) == true
            } else {
                itemTop != baseHeight
            }
            if (viewHolder != null) {
                directionDown = itemTop > baseHeight
            } else {
                if (lastFocusPosition + 1 < firstVisibleItem ?: 0) {
                    directionDown = false
                } else if (lastFocusPosition + 1 > lastVisibleItem ?: 0) {
                    directionDown = true
                }
            }
            DebugUtil.i(
                TAG,
                "getLocationBackShowAndDown " +
                        "itemTop == $itemTop," +
                        " baseHeight == $baseHeight," +
                        "offset == $offset," +
                        "position == $lastFocusPosition" +
                        "shouldShow == $shouldShow" +
                        "directionDown == $directionDown"
            )
        }
        return Pair(shouldShow, directionDown)
    }

    /**
     * 根据列表滚动距离更新沉浸态状态值
     * @param dy 列表垂直滚动距离
     */
    private fun updateIsImmersiveState(dy: Int) {
        mViewModel?.apply {
            if (isImmersiveAnimationRunning) {
                //沉浸态动画切换会影响列表高度，会引起列表滚动回调，从而会干扰滚动判断，所以动画执行过程中不处理滚动回调
                return
            }
            //是否是沉浸态标记
            val isImmersive = isImmersiveState.value == true
            /* immersiveScrollDistance是列表滚动累加的距离，达到switchImmersiveStateDistance进入沉浸态
            减小到0是退出沉浸态，始终保持 0 <= immersiveScrollDistance <= switchImmersiveStateDistance*/
            if ((dy > 0 && immersiveScrollDistance < switchImmersiveStateDistance) ||
                (dy < 0 && immersiveScrollDistance > 0)
            ) {
                val totalY = immersiveScrollDistance + dy
                immersiveScrollDistance = if (totalY > switchImmersiveStateDistance) {
                    switchImmersiveStateDistance
                } else if (totalY < 0) {
                    0
                } else {
                    totalY
                }
                immersiveMoveDownDistance.value =
                    (immersiveScrollDistance * moveDownDistanceRatio).toInt()
            }
            if (!isImmersive && immersiveScrollDistance >= switchImmersiveStateDistance) {
                // 进入沉浸态
                isImmersiveState.value = true
            } else if (isImmersive && immersiveScrollDistance <= 0) {
                // 退出沉浸态
                isImmersiveState.value = false
            }
        }
    }
}